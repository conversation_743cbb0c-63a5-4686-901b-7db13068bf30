/*
  (C) Copyright 2023-2025 by Shanghai Huishui Tech Co., LTD.  All rights reserved.

  This computer source code and related instructions and comments are the
  unpublished confidential and proprietary information of Shanghai Huishui
  Tech Co., LTD. and are protected under applicable copyright and trade
  secret law.  They may not be disclosed to, copied or used by any third
  party without the prior written consent of Shanghai Huishui Tech Co., LTD.
*/

import { Tag } from 'antd';
import 'ol/ol.css';
import styled from 'styled-components';
import 'ol-contextmenu/ol-contextmenu.css';
import { EllipsisText } from 'src/styles/common-style';

const MapViewWrapper = styled.div`
  width: 100%;
  height: 100%;
  background: ${({ theme }) => theme.colorBgBase};
  .ol-scale-line {
    bottom: 25px;
  }

  .ol-zoom {
    display: none;
    top: auto;
    bottom: 35px;
  }

  .ol-zoom-extent {
    top: auto;
    bottom: 85px;
    button {
      line-height: 1;
    }
  }

  .ol-mouse-position {
    top: auto;
    left: 10px;
    bottom: 3px;
  }

  .cursorCell {
    cursor: cell;
  }

  .cursorPointer {
    cursor: copy;
  }

  .cursorCrosshair {
    cursor: crosshair;
  }

  .event-overlay-high-z {
    z-index: 10 !important;
  }

  .event-overlay-container {
    z-index: 10 !important;
  }

  .event-tooltip {
    z-index: 9999999 !important;
    position: absolute !important;
    pointer-events: auto !important;
  }

  .ol-overlay-container {
    pointer-events: auto !important;
  }

  .tooltip-wrap.tooltip-static .tipClose {
    display: block;
  }
  .tooltip-measure {
    opacity: 1;
    font-weight: bold;
  }
  .tooltip-static {
    background-color: #ffcc33;
    color: black;
    border: 1px solid white;
  }
  .tooltip-measure:before,
  .tooltip-static:before {
    border-top: 6px solid rgba(255, 255, 255, 0.8);
    border-right: 6px solid transparent;
    border-left: 6px solid transparent;
    content: '';
    position: absolute;
    bottom: -6px;
    margin-left: -7px;
    left: 50%;
  }
  .tooltip-static:before {
    border-top-color: #ffcc33;
  }
  .ol-popup-closer {
    text-decoration: none;
    position: absolute;
    top: 4px;
    right: 8px;
    color: red;
  }
  .ol-popup-closer:after {
    content: 'X';
  }
  /*MeasureTool*/

  .MeasureTool {
    position: absolute;
    top: 2em;
    right: 5em;
    text-align: left;
    padding: 0;
  }
  .MeasureTool .ulbody {
    display: none;
  }
  .MeasureTool.shown .ulbody {
    display: block;
  }
  .ulbody li input:focus,
  .ulbody li input:hover {
    background-color: white;
    color: blue;
    font-weight: bold;
  }
  .MeasureTool ul {
    padding: 0;
    list-style: none;
    margin: 0;
  }
  .MeasureTool ul li {
    text-align: center;
  }
  .MeasureTool > ul > li > input {
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAABiUlEQVRIS8WUQU7CQBSG/ze6lxsoJxCPYMpePIG4almJW2giJq1xJ6ykbuAG6p6GIwgnkBsYtwb7zGsZLKUk0tI4m6btzP/N/+bNTyh4UMH6+D+A7fkdACwOHdO4TTq1vVENoGP9PW2O/NvowPb8UDwafO6Y1Zc4xO77ExDigFStvwEYPccymhrQGYxL86/gIw50TCM7gBkz1zLKWrD16NeVwgCMqXaRGcDgTwIdzANVvm+czgTS9kZDAl0AkLO5WZxTNgfM/EpEZwxcu6bRjQD+jIDDgNSJ4uAtFyAIcCnlEJBrVWutp3FFRMWZa1ZLuhkyl0hKs6+Cd73Ltuc3CXjQwNwA2ZkuibQrM+pSMnF21zCGOwHYfb8LwhUYPSauxw99N4Do1j7/3jtMHcuoyHsegESFxET4lN7Hnpoo/j6Kvkc3exEpy3nJSCk+7OKhtinYton0pB6thlooFZYkx1hZnwbIob2+NA2wlv1bEsNs0kMAK5Z012wpupye1Cu+i7Lu9K/rCnfwA90A1i8VnCB2AAAAAElFTkSuQmCC')
      /*logo.png*/;
    background-position: center center;
    background-repeat: no-repeat;
  }
  .MeasureTool input[type='button'] {
    background-color: rgba(255, 255, 255, 0.7);
    width: 60px;
    height: 26px;
    border: 0;
  }
  .MeasureTool .ulbody li {
    border-top: 1px solid rgba(221, 221, 221, 0.7);
  }
  .zIndex0 {
    z-index: 6 !important;
  }
  .zIndex1 {
    z-index: 5 !important;
  }
  .zIndex2 {
    z-index: 4 !important;
  }
  .zIndex3 {
    z-index: 3 !important;
  }
  .zIndex4 {
    z-index: 2 !important;
  }
  .zIndex5 {
    z-index: 1 !important;
  }
  .zIndex9999 {
    z-index: 0 !important;
  }
`;

export const SolutionNameTag = styled(Tag)`
  display: inline-block;
  padding: 3px 5px;
  position: absolute;
  bottom: 50px;
  left: 10px;
  max-width: 150px;
  ${EllipsisText}
`;

export const CompareWrapper = styled.div`
  height: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  background-color: ${({ theme }) => theme.colorBgBase};
`;

export default MapViewWrapper;
